use crate::error::BurnitError;
use crate::result::{detect_json_result, store_result};
use log::{debug, info};
use std::path::Path;
use std::process::{Command, Stdio};
use std::time::Instant;
use sysinfo::System;



/// Execute a command with result capture and JSON detection
pub fn execute_command_with_result_capture(
    command: &str,
    working_dir: &Path,
    _git_ref: &str
) -> Result<(), BurnitError> {
    info!("Executing command: {} in directory: {:?}", command, working_dir);

    // Parse the command string into command and arguments
    let parts: Vec<&str> = command.split_whitespace().collect();
    if parts.is_empty() {
        return Err(BurnitError::CommandError("Empty command".to_string()));
    }

    let (cmd, args) = parts.split_first().unwrap();

    debug!("Command: {}, Args: {:?}", cmd, args);

    // Start timing
    let start_time = Instant::now();
    let mut system = System::new_all();
    system.refresh_all();

    // Execute the command with captured output
    let output = Command::new(cmd)
        .args(args)
        .current_dir(working_dir)
        .stdout(Stdio::piped())
        .stderr(Stdio::piped())
        .output()
        .map_err(|e| BurnitError::CommandError(format!("Failed to execute command '{}': {}", cmd, e)))?;

    let _elapsed = start_time.elapsed();

    // Get memory usage (simplified - we'll use system memory as a proxy)
    system.refresh_all();
    let _total_memory = system.total_memory() as f64 / 1024.0 / 1024.0; // Convert to MB
    let _used_memory = system.used_memory() as f64 / 1024.0 / 1024.0; // Convert to MB

    // Convert output to string
    let stdout_str = String::from_utf8_lossy(&output.stdout);
    let stderr_str = String::from_utf8_lossy(&output.stderr);

    // Print output to console (maintain existing behavior)
    if !stdout_str.is_empty() {
        print!("{}", stdout_str);
    }
    if !stderr_str.is_empty() {
        eprint!("{}", stderr_str);
    }

    // Check if command succeeded
    let success = output.status.success();
    let _exit_code = output.status.code().unwrap_or(-1);

    if !success {
        let error_msg = match output.status.code() {
            Some(code) => format!("Command '{}' failed with exit code: {}", command, code),
            None => format!("Command '{}' was terminated by signal", command),
        };
        return Err(BurnitError::CommandError(error_msg));
    }

    // Try to detect JSON result in stdout
    if let Some(json_result) = detect_json_result(&stdout_str) {
        info!("Detected JSON result from command output");

        // Store the result in burnit-results.json
        let results_file = working_dir.join("burnit-results.json");
        if let Err(e) = store_result(json_result, &results_file) {
            // Log error but don't fail the command execution
            log::warn!("Failed to store JSON result: {}", e);
        }
    } else {
        debug!("Command output does not contain valid JSON result");
    }

    info!("Command completed successfully");
    Ok(())
}
