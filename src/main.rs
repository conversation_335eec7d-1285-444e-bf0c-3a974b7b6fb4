use clap::Parser;
use log::{error, info};
use std::process;

mod command;
mod error;
mod git;
mod result;

use crate::error::BurnitError;

/// Execute commands against different Git references while safely managing Git state
#[derive(Parser)]
#[command(name = "burnit")]
#[command(about = "Execute commands against different Git references while safely managing Git state")]
#[command(version)]
struct Args {
    /// Path to the Git repository
    repository_path: std::path::PathBuf,

    /// Command to execute in the repository
    #[arg(short, long)]
    command: String,

    /// Git reference (branch, commit SHA, or tag) to checkout
    #[arg(short, long)]
    r#ref: String,
}

fn main() {
    env_logger::init();

    let args = Args::parse();

    if let Err(e) = run(args) {
        error!("Error: {}", e);
        process::exit(1);
    }
}

fn run(args: Args) -> Result<(), BurnitError> {
    info!("Starting burnit execution");
    info!("Repository: {:?}", args.repository_path);
    info!("Command: {}", args.command);
    info!("Reference: {}", args.r#ref);

    // Validate repository path exists and is a Git repository
    if !args.repository_path.exists() {
        return Err(BurnitError::RepositoryNotFound(args.repository_path));
    }

    // Open the Git repository
    let repo = git::open_repository(&args.repository_path)?;

    // Store current Git state
    let original_state = git::get_current_state(&repo)?;
    info!("Current Git state: {:?}", original_state);

    // Execute the operation with automatic cleanup
    let result = execute_with_cleanup(&repo, &args, &original_state);

    match result {
        Ok(_) => {
            info!("Command executed successfully");
            Ok(())
        }
        Err(e) => {
            error!("Command execution failed: {}", e);
            Err(e)
        }
    }
}

fn execute_with_cleanup(
    repo: &git2::Repository,
    args: &Args,
    original_state: &git::GitState,
) -> Result<(), BurnitError> {
    // Ensure we restore state even if something goes wrong
    let _guard = git::StateGuard::new(repo, original_state)?;

    // Checkout the specified reference
    git::checkout_reference(repo, &args.r#ref)?;
    info!("Checked out reference: {}", args.r#ref);

    // Execute the command with result capture
    command::execute_command_with_result_capture(&args.command, &args.repository_path, &args.r#ref)?;
    info!("Command completed successfully");

    Ok(())
}
