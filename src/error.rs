use std::path::PathBuf;
use thiserror::Error;

/// Errors that can occur during burnit execution
#[derive(Error, Debug)]
pub enum BurnitError {
    /// Repository path does not exist
    #[error("Repository not found at path: {0:?}")]
    RepositoryNotFound(PathBuf),

    /// Path exists but is not a valid Git repository
    #[error("Path is not a valid Git repository: {0:?}")]
    InvalidRepository(PathBuf),

    /// Git reference (branch, commit, tag) does not exist
    #[error("Git reference not found: {0}")]
    ReferenceNotFound(String),

    /// Failed to get current Git state
    #[error("Failed to get current Git state: {0}")]
    GitStateError(String),

    /// Failed to checkout Git reference
    #[error("Failed to checkout reference '{0}': {1}")]
    CheckoutError(String, String),

    /// Failed to restore original Git state
    #[error("Failed to restore original Git state: {0}")]
    RestoreError(String),

    /// Command execution failed
    #[error("Command execution failed: {0}")]
    CommandError(String),

    /// Git operation failed
    #[error("Git operation failed: {0}")]
    GitError(#[from] git2::Error),

    /// I/O operation failed
    #[error("I/O error: {0}")]
    IoError(#[from] std::io::Error),

    /// Result processing failed
    #[error("Result processing error: {0}")]
    ResultError(String),
}
