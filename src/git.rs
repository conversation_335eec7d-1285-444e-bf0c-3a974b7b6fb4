use crate::error::BurnitError;
use git2::{Repository, RepositoryState, BranchType, ObjectType};
use log::{debug, info, warn};
use std::path::Path;

/// Represents the current state of a Git repository
#[derive(Debug, Clone)]
pub struct GitState {
    /// Current HEAD reference (branch name or commit SHA)
    pub head_ref: String,
    /// Whether the repository has uncommitted changes
    pub has_uncommitted_changes: bool,
    /// Current repository state (normal, merge, rebase, etc.)
    pub repo_state: RepositoryState,
}

/// RAII guard that ensures Git state is restored when dropped
pub struct StateGuard<'a> {
    repo: &'a Repository,
    original_state: GitState,
}

impl<'a> StateGuard<'a> {
    pub fn new(repo: &'a Repository, original_state: &GitState) -> Result<Self, BurnitError> {
        Ok(StateGuard {
            repo,
            original_state: original_state.clone(),
        })
    }
}

impl<'a> Drop for StateGuard<'a> {
    fn drop(&mut self) {
        if let Err(e) = restore_git_state(self.repo, &self.original_state) {
            // Log error but don't panic in destructor
            log::error!("Failed to restore Git state: {}", e);
        }
    }
}

/// Open a Git repository at the specified path
pub fn open_repository(path: &Path) -> Result<Repository, BurnitError> {
    Repository::open(path).map_err(|e| {
        if e.code() == git2::ErrorCode::NotFound {
            BurnitError::InvalidRepository(path.to_path_buf())
        } else {
            BurnitError::GitError(e)
        }
    })
}

/// Get the current state of the Git repository
pub fn get_current_state(repo: &Repository) -> Result<GitState, BurnitError> {
    let head = repo.head().map_err(|e| {
        BurnitError::GitStateError(format!("Failed to get HEAD: {}", e))
    })?;

    let head_ref = if head.is_branch() {
        // Get branch name
        head.shorthand()
            .ok_or_else(|| BurnitError::GitStateError("Invalid branch name".to_string()))?
            .to_string()
    } else {
        // Get commit SHA
        head.target()
            .ok_or_else(|| BurnitError::GitStateError("HEAD has no target".to_string()))?
            .to_string()
    };

    let has_uncommitted_changes = check_uncommitted_changes(repo)?;
    let repo_state = repo.state();

    Ok(GitState {
        head_ref,
        has_uncommitted_changes,
        repo_state,
    })
}

/// Check if the repository has uncommitted changes
fn check_uncommitted_changes(repo: &Repository) -> Result<bool, BurnitError> {
    let statuses = repo.statuses(None)?;
    Ok(!statuses.is_empty())
}

/// Checkout a specific Git reference (branch, commit, or tag)
pub fn checkout_reference(repo: &Repository, reference: &str) -> Result<(), BurnitError> {
    debug!("Attempting to checkout reference: {}", reference);

    // First, try to resolve the reference
    let object = resolve_reference(repo, reference)?;

    // Perform the checkout with force to handle conflicts
    let mut checkout_builder = git2::build::CheckoutBuilder::new();
    checkout_builder.force(); // Use force instead of safe to handle conflicts

    repo.checkout_tree(&object, Some(&mut checkout_builder))
        .map_err(|e| BurnitError::CheckoutError(reference.to_string(), e.to_string()))?;

    // Update HEAD to point to the new reference
    if let Ok(_branch) = repo.find_branch(reference, BranchType::Local) {
        // It's a local branch, set HEAD to point to it
        repo.set_head(&format!("refs/heads/{}", reference))
            .map_err(|e| BurnitError::CheckoutError(reference.to_string(), e.to_string()))?;
    } else {
        // It's a commit or tag, detach HEAD
        repo.set_head_detached(object.id())
            .map_err(|e| BurnitError::CheckoutError(reference.to_string(), e.to_string()))?;
    }

    info!("Successfully checked out reference: {}", reference);
    Ok(())
}

/// Resolve a Git reference to a Git object
fn resolve_reference<'a>(repo: &'a Repository, reference: &str) -> Result<git2::Object<'a>, BurnitError> {
    // Try different ways to resolve the reference

    // 1. Try as a direct reference (branch, tag)
    if let Ok(reference_obj) = repo.find_reference(reference) {
        if let Some(target) = reference_obj.target() {
            return repo.find_object(target, None)
                .map_err(|_| BurnitError::ReferenceNotFound(reference.to_string()));
        }
    }

    // 2. Try as a branch name
    if let Ok(branch) = repo.find_branch(reference, BranchType::Local) {
        if let Some(target) = branch.get().target() {
            return repo.find_object(target, None)
                .map_err(|_| BurnitError::ReferenceNotFound(reference.to_string()));
        }
    }

    // 3. Try as a remote branch
    if let Ok(branch) = repo.find_branch(reference, BranchType::Remote) {
        if let Some(target) = branch.get().target() {
            return repo.find_object(target, None)
                .map_err(|_| BurnitError::ReferenceNotFound(reference.to_string()));
        }
    }

    // 4. Try as a tag
    if let Ok(tag_ref) = repo.find_reference(&format!("refs/tags/{}", reference)) {
        if let Some(target) = tag_ref.target() {
            let obj = repo.find_object(target, None)
                .map_err(|_| BurnitError::ReferenceNotFound(reference.to_string()))?;

            // If it's an annotated tag, get the target object
            if obj.kind() == Some(ObjectType::Tag) {
                if let Some(tag) = obj.as_tag() {
                    return Ok(tag.target().map_err(|_| BurnitError::ReferenceNotFound(reference.to_string()))?);
                }
            }
            return Ok(obj);
        }
    }

    // 5. Try as a commit SHA (full or short)
    if reference.len() >= 4 && reference.chars().all(|c| c.is_ascii_hexdigit()) {
        // Try as full SHA first
        if reference.len() == 40 {
            if let Ok(oid) = git2::Oid::from_str(reference) {
                return repo.find_object(oid, Some(ObjectType::Commit))
                    .map_err(|_| BurnitError::ReferenceNotFound(reference.to_string()));
            }
        }

        // Try as short SHA using revparse
        if let Ok(object) = repo.revparse_single(reference) {
            return Ok(object);
        }
    }

    Err(BurnitError::ReferenceNotFound(reference.to_string()))
}

/// Restore the Git repository to its original state
fn restore_git_state(repo: &Repository, original_state: &GitState) -> Result<(), BurnitError> {
    info!("Restoring Git state to: {:?}", original_state);

    // Check if we need to warn about uncommitted changes
    if original_state.has_uncommitted_changes {
        warn!("Original state had uncommitted changes - these may be lost during restore");
    }

    // Restore the original HEAD reference
    if original_state.head_ref.len() == 40 && original_state.head_ref.chars().all(|c| c.is_ascii_hexdigit()) {
        // It was a detached HEAD (commit SHA)
        let oid = git2::Oid::from_str(&original_state.head_ref)
            .map_err(|e| BurnitError::RestoreError(format!("Invalid commit SHA: {}", e)))?;
        repo.set_head_detached(oid)
            .map_err(|e| BurnitError::RestoreError(e.to_string()))?;
    } else {
        // It was a branch
        repo.set_head(&format!("refs/heads/{}", original_state.head_ref))
            .map_err(|e| BurnitError::RestoreError(e.to_string()))?;
    }

    // Checkout the working directory to match HEAD
    let head = repo.head()
        .map_err(|e| BurnitError::RestoreError(format!("Failed to get HEAD after restore: {}", e)))?;
    let object = head.peel_to_tree()
        .map_err(|e| BurnitError::RestoreError(format!("Failed to get tree: {}", e)))?;

    let mut checkout_builder = git2::build::CheckoutBuilder::new();
    checkout_builder.force(); // Use force to ensure clean restoration

    repo.checkout_tree(object.as_object(), Some(&mut checkout_builder))
        .map_err(|e| BurnitError::RestoreError(e.to_string()))?;

    info!("Successfully restored Git state");
    Ok(())
}
