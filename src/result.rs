use crate::error::BurnitError;
use log::{debug, info};
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::Path;

/// JSON schema for capturing command execution results
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CommandResult {
    pub task: TaskInfo,
    pub performance: PerformanceMetrics,
    pub status: ExecutionStatus,
    pub metadata: ResultMetadata,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct TaskInfo {
    pub name: String,
    pub description: String,
    pub command: String,
    pub git_reference: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct PerformanceMetrics {
    pub memory_usage: MemoryUsage,
    pub execution_time: ExecutionTime,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub struct MemoryUsage {
    pub peak_memory_mb: f64,
    pub average_memory_mb: f64,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub struct ExecutionTime {
    pub elapsed_seconds: f64,
    pub start_time: String,
    pub end_time: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct ExecutionStatus {
    pub success: bool,
    pub exit_code: i32,
    pub error_message: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct ResultMetadata {
    pub burnit_version: String,
    pub timestamp: String,
    pub repository_path: String,
}

/// Container for storing multiple command results
#[derive(Debug, Serialize, Deserialize)]
pub struct ResultsContainer {
    pub results: Vec<CommandResult>,
}

impl ResultsContainer {
    pub fn new() -> Self {
        Self {
            results: Vec::new(),
        }
    }

    pub fn add_result(&mut self, result: CommandResult) {
        self.results.push(result);
    }
}

/// Detect if command output contains valid JSON matching our schema
pub fn detect_json_result(output: &str) -> Option<CommandResult> {
    debug!("Attempting to parse command output as JSON result");

    // Try to parse the output as JSON
    match serde_json::from_str::<CommandResult>(output.trim()) {
        Ok(result) => {
            info!("Successfully parsed command output as JSON result");
            Some(result)
        }
        Err(e) => {
            debug!("Command output is not valid JSON result: {}", e);
            None
        }
    }
}

/// Store command result in burnit-results.json file
pub fn store_result(result: CommandResult, results_file_path: &Path) -> Result<(), BurnitError> {
    info!("Storing command result to: {:?}", results_file_path);

    // Load existing results or create new container
    let mut container = if results_file_path.exists() {
        let content = fs::read_to_string(results_file_path)
            .map_err(|e| BurnitError::IoError(e))?;

        serde_json::from_str::<ResultsContainer>(&content)
            .map_err(|e| BurnitError::ResultError(format!("Failed to parse existing results file: {}", e)))?
    } else {
        ResultsContainer::new()
    };

    // Add the new result
    container.add_result(result);

    // Write back to file
    let json_content = serde_json::to_string_pretty(&container)
        .map_err(|e| BurnitError::ResultError(format!("Failed to serialize results: {}", e)))?;

    fs::write(results_file_path, json_content)
        .map_err(|e| BurnitError::IoError(e))?;

    info!("Successfully stored command result");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[test]
    fn test_detect_valid_json_result() {
        let json_output = r#"
        {
          "task": {
            "name": "test-task",
            "description": "A test task",
            "command": "echo test",
            "git_reference": "main"
          },
          "performance": {
            "memory_usage": {
              "peak_memory_mb": 45.2,
              "average_memory_mb": 32.1
            },
            "execution_time": {
              "elapsed_seconds": 2.5,
              "start_time": "2024-01-15T10:30:00Z",
              "end_time": "2024-01-15T10:30:02.5Z"
            }
          },
          "status": {
            "success": true,
            "exit_code": 0,
            "error_message": null
          },
          "metadata": {
            "burnit_version": "0.1.0",
            "timestamp": "2024-01-15T10:30:02.5Z",
            "repository_path": "/path/to/repo"
          }
        }
        "#;

        let result = detect_json_result(json_output);
        assert!(result.is_some());

        let result = result.unwrap();
        assert_eq!(result.task.name, "test-task");
        assert_eq!(result.status.success, true);
        assert_eq!(result.performance.memory_usage.peak_memory_mb, 45.2);
    }

    #[test]
    fn test_detect_invalid_json_result() {
        let invalid_output = "This is not JSON";
        let result = detect_json_result(invalid_output);
        assert!(result.is_none());
    }

    #[test]
    fn test_detect_malformed_json_result() {
        let malformed_json = r#"{"task": {"name": "incomplete"}"#;
        let result = detect_json_result(malformed_json);
        assert!(result.is_none());
    }

    #[test]
    fn test_store_result_new_file() {
        let temp_dir = TempDir::new().unwrap();
        let results_file = temp_dir.path().join("burnit-results.json");

        let result = CommandResult {
            task: TaskInfo {
                name: "test-task".to_string(),
                description: "Test description".to_string(),
                command: "echo test".to_string(),
                git_reference: "main".to_string(),
            },
            performance: PerformanceMetrics {
                memory_usage: MemoryUsage {
                    peak_memory_mb: 45.2,
                    average_memory_mb: 32.1,
                },
                execution_time: ExecutionTime {
                    elapsed_seconds: 2.5,
                    start_time: "2024-01-15T10:30:00Z".to_string(),
                    end_time: "2024-01-15T10:30:02.5Z".to_string(),
                },
            },
            status: ExecutionStatus {
                success: true,
                exit_code: 0,
                error_message: None,
            },
            metadata: ResultMetadata {
                burnit_version: "0.1.0".to_string(),
                timestamp: "2024-01-15T10:30:02.5Z".to_string(),
                repository_path: "/path/to/repo".to_string(),
            },
        };

        store_result(result.clone(), &results_file).unwrap();

        // Verify file was created and contains the result
        assert!(results_file.exists());

        let content = fs::read_to_string(&results_file).unwrap();
        let container: ResultsContainer = serde_json::from_str(&content).unwrap();

        assert_eq!(container.results.len(), 1);
        assert_eq!(container.results[0], result);
    }

    #[test]
    fn test_store_result_append_to_existing() {
        let temp_dir = TempDir::new().unwrap();
        let results_file = temp_dir.path().join("burnit-results.json");

        // Create initial result
        let result1 = CommandResult {
            task: TaskInfo {
                name: "task-1".to_string(),
                description: "First task".to_string(),
                command: "echo first".to_string(),
                git_reference: "main".to_string(),
            },
            performance: PerformanceMetrics {
                memory_usage: MemoryUsage {
                    peak_memory_mb: 30.0,
                    average_memory_mb: 25.0,
                },
                execution_time: ExecutionTime {
                    elapsed_seconds: 1.0,
                    start_time: "2024-01-15T10:30:00Z".to_string(),
                    end_time: "2024-01-15T10:30:01Z".to_string(),
                },
            },
            status: ExecutionStatus {
                success: true,
                exit_code: 0,
                error_message: None,
            },
            metadata: ResultMetadata {
                burnit_version: "0.1.0".to_string(),
                timestamp: "2024-01-15T10:30:01Z".to_string(),
                repository_path: "/path/to/repo".to_string(),
            },
        };

        store_result(result1.clone(), &results_file).unwrap();

        // Add second result
        let result2 = CommandResult {
            task: TaskInfo {
                name: "task-2".to_string(),
                description: "Second task".to_string(),
                command: "echo second".to_string(),
                git_reference: "feature".to_string(),
            },
            performance: PerformanceMetrics {
                memory_usage: MemoryUsage {
                    peak_memory_mb: 40.0,
                    average_memory_mb: 35.0,
                },
                execution_time: ExecutionTime {
                    elapsed_seconds: 2.0,
                    start_time: "2024-01-15T10:31:00Z".to_string(),
                    end_time: "2024-01-15T10:31:02Z".to_string(),
                },
            },
            status: ExecutionStatus {
                success: true,
                exit_code: 0,
                error_message: None,
            },
            metadata: ResultMetadata {
                burnit_version: "0.1.0".to_string(),
                timestamp: "2024-01-15T10:31:02Z".to_string(),
                repository_path: "/path/to/repo".to_string(),
            },
        };

        store_result(result2.clone(), &results_file).unwrap();

        // Verify both results are stored
        let content = fs::read_to_string(&results_file).unwrap();
        let container: ResultsContainer = serde_json::from_str(&content).unwrap();

        assert_eq!(container.results.len(), 2);
        assert_eq!(container.results[0], result1);
        assert_eq!(container.results[1], result2);
    }
}
