{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"openssl-on-win32\", \"openssl-sys\", \"vendored-openssl\", \"zlib-ng-compat\"]", "target": 8028971792975562915, "profile": 2040997289075261528, "path": 5686632740480329119, "deps": [[2924422107542798392, "libc", false, 5153261197468970866], [9070360545695802481, "openssl_sys", false, 4750529406576097491], [17022423707615322322, "libz_sys", false, 5939777213568020678], [17372192778073352438, "build_script_build", false, 13439537057158534635]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/libssh2-sys-0e2f974bbf1b4806/dep-lib-libssh2_sys", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}