{"rustc": 15497389221046826682, "features": "[\"https\", \"libssh2-sys\", \"openssl-sys\", \"ssh\", \"ssh_key_from_memory\"]", "declared_features": "[\"https\", \"libssh2-sys\", \"openssl-sys\", \"ssh\", \"ssh_key_from_memory\", \"vendored\", \"vendored-openssl\", \"zlib-ng-compat\"]", "target": 8268026152342382216, "profile": 2040997289075261528, "path": 17556353820388644196, "deps": [[2924422107542798392, "libc", false, 5153261197468970866], [9070360545695802481, "openssl_sys", false, 4750529406576097491], [11857865261928459945, "build_script_build", false, 269632952958344365], [17022423707615322322, "libz_sys", false, 5939777213568020678], [17372192778073352438, "libssh2_sys", false, 9623644430868758062]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/libgit2-sys-38ccdc145a8b6a50/dep-lib-libgit2_sys", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}