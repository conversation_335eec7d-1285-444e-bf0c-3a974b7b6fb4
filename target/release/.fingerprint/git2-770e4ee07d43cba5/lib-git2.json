{"rustc": 15497389221046826682, "features": "[\"default\", \"https\", \"openssl-probe\", \"openssl-sys\", \"ssh\", \"ssh_key_from_memory\"]", "declared_features": "[\"default\", \"https\", \"openssl-probe\", \"openssl-sys\", \"ssh\", \"ssh_key_from_memory\", \"unstable\", \"vendored-libgit2\", \"vendored-openssl\", \"zlib-ng-compat\"]", "target": 17727337184649825680, "profile": 2040997289075261528, "path": 3628925524361347364, "deps": [[2924422107542798392, "libc", false, 5153261197468970866], [3150220818285335163, "url", false, 6757830781215600944], [5986029879202738730, "log", false, 6393240213201396425], [7896293946984509699, "bitflags", false, 14775306675007527678], [11857865261928459945, "libgit2_sys", false, 6193441483432974222]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/git2-770e4ee07d43cba5/dep-lib-git2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}