{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[17372192778073352438, "build_script_build", false, 2504381154436778321], [17022423707615322322, "build_script_build", false, 16350780799121449083], [9070360545695802481, "build_script_main", false, 4157926615130979730]], "local": [{"RerunIfEnvChanged": {"var": "LIBSSH2_SYS_USE_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "DEP_Z_INCLUDE", "val": null}}, {"RerunIfEnvChanged": {"var": "DEP_OPENSSL_INCLUDE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "MACOSX_DEPLOYMENT_TARGET", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_AR", "val": null}}, {"RerunIfEnvChanged": {"var": "AR", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_aarch64-apple-darwin", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}