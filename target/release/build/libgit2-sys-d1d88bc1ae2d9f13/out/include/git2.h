/*
 * Copyright (C) the libgit2 contributors. All rights reserved.
 *
 * This file is part of libgit2, distributed under the GNU GPL v2 with
 * a Linking Exception. For full terms see the included COPYING file.
 */

#ifndef INCLUDE_git_git_h__
#define INCLUDE_git_git_h__

#include "git2/annotated_commit.h"
#include "git2/apply.h"
#include "git2/attr.h"
#include "git2/blob.h"
#include "git2/blame.h"
#include "git2/branch.h"
#include "git2/buffer.h"
#include "git2/cert.h"
#include "git2/checkout.h"
#include "git2/cherrypick.h"
#include "git2/clone.h"
#include "git2/commit.h"
#include "git2/common.h"
#include "git2/config.h"
#include "git2/credential.h"
#include "git2/deprecated.h"
#include "git2/describe.h"
#include "git2/diff.h"
#include "git2/email.h"
#include "git2/errors.h"
#include "git2/experimental.h"
#include "git2/filter.h"
#include "git2/global.h"
#include "git2/graph.h"
#include "git2/ignore.h"
#include "git2/index.h"
#include "git2/indexer.h"
#include "git2/mailmap.h"
#include "git2/merge.h"
#include "git2/message.h"
#include "git2/net.h"
#include "git2/notes.h"
#include "git2/object.h"
#include "git2/odb.h"
#include "git2/odb_backend.h"
#include "git2/oid.h"
#include "git2/pack.h"
#include "git2/patch.h"
#include "git2/pathspec.h"
#include "git2/proxy.h"
#include "git2/rebase.h"
#include "git2/refdb.h"
#include "git2/reflog.h"
#include "git2/refs.h"
#include "git2/refspec.h"
#include "git2/remote.h"
#include "git2/repository.h"
#include "git2/reset.h"
#include "git2/revert.h"
#include "git2/revparse.h"
#include "git2/revwalk.h"
#include "git2/signature.h"
#include "git2/stash.h"
#include "git2/status.h"
#include "git2/submodule.h"
#include "git2/tag.h"
#include "git2/transport.h"
#include "git2/transaction.h"
#include "git2/tree.h"
#include "git2/types.h"
#include "git2/version.h"
#include "git2/worktree.h"

#endif
