{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 4507022764392120844, "profile": 15057526963834790232, "path": 841814534677975512, "deps": [[1441306149310335789, "tempfile", false, 11170551224554421210], [3283849480659401926, "burnit", false, 2044596094339783055], [5986029879202738730, "log", false, 8611220590675663417], [8008191657135824715, "thiserror", false, 8844068926679323047], [9689903380558560274, "serde", false, 9197440507941082661], [9963614578868468249, "sysinfo", false, 8052068432743137685], [12103695930867503580, "env_logger", false, 12566414645821328595], [12939671402123591185, "assert_cmd", false, 14435126868520731151], [13625485746686963219, "anyhow", false, 16560158462299938842], [15367738274754116744, "serde_json", false, 5177400542639947154], [15863765456528386755, "predicates", false, 14656539466547602959], [17240636971104806819, "git2", false, 15368825238970657965], [17791399664576300066, "clap", false, 10276606134326082094]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/burnit-261e1f4e9e1d0101/dep-test-integration-test-result_capture_tests", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}