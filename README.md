# Burnit

A command-line tool that executes arbitrary commands against different Git references (commits, branches, tags) in a local Git repository while safely managing Git state.

## Features

- **Safe Git State Management**: Automatically stores and restores the original Git state
- **Flexible Reference Support**: Works with branches, commit SHAs, and tags
- **JSON Result Capture**: Automatically detects and stores structured command results
- **Robust Error Handling**: Comprehensive validation and error reporting
- **Structured Logging**: Detailed logging for debugging and monitoring
- **Cross-Platform**: Built with Rust for reliable cross-platform support

## Installation

### From Source

```bash
git clone https://github.com/your-username/burnit
cd burnit
cargo build --release
```

The binary will be available at `target/release/burnit`.

### Using Cargo

```bash
cargo install burnit
```

## Usage

```bash
burnit <repository_path> --command "<command_to_execute>" --ref <git_reference>
```

### Arguments

- `repository_path`: Path to the Git repository
- `--command, -c`: Command to execute in the repository directory
- `--ref, -r`: Git reference to checkout (branch, commit SHA, or tag)

### Examples

#### Execute command on a specific branch
```bash
burnit path/to/repo --command "npm test" --ref main
burnit path/to/repo --command "cargo build" --ref feature/new-api
```

#### Execute command on a specific commit
```bash
burnit path/to/repo --command "bin/benchmark" --ref cafd4cdcd3a99fbef6cb849db9d72afbc0841e33
burnit path/to/repo --command "make test" --ref abc123f
```

#### Execute command on a tag
```bash
burnit path/to/repo --command "bin/benchmark" --ref v1.0.0
burnit path/to/repo --command "npm run build" --ref release-2.1
```

#### Complex commands
```bash
burnit path/to/repo --command "cargo test --release -- --nocapture" --ref main
burnit path/to/repo --command "docker build -t myapp:test ." --ref feature/docker
```

#### Commands with JSON result capture
```bash
# If your command outputs JSON matching the schema, results are automatically captured
burnit path/to/repo --command "./performance-test.sh" --ref main
burnit path/to/repo --command "npm run benchmark" --ref v1.0.0

# Check captured results
cat path/to/repo/burnit-results.json
```

## How It Works

1. **Validation**: Verifies that the repository path exists and is a valid Git repository
2. **State Capture**: Records the current Git state (branch/commit, uncommitted changes, repository state)
3. **Reference Resolution**: Resolves the specified reference (branch, commit, or tag)
4. **Checkout**: Safely checks out the target reference
5. **Command Execution**: Runs the specified command in the repository directory
6. **Result Detection**: Automatically detects if command output contains valid JSON results
7. **Result Storage**: Stores JSON results in `burnit-results.json` for analysis
8. **State Restoration**: Automatically restores the original Git state, even if the command fails

## JSON Result Capture

Burnit automatically detects when a command outputs valid JSON that matches a predefined schema and stores these results in a `burnit-results.json` file in the repository root. This feature is designed for commands that output performance metrics, test results, or other structured data.

### JSON Schema

Commands that output JSON matching this schema will have their results automatically captured:

```json
{
  "task": {
    "name": "string",
    "description": "string",
    "command": "string",
    "git_reference": "string"
  },
  "performance": {
    "memory_usage": {
      "peak_memory_mb": 45.2,
      "average_memory_mb": 32.1
    },
    "execution_time": {
      "elapsed_seconds": 2.5,
      "start_time": "2024-01-15T10:30:00Z",
      "end_time": "2024-01-15T10:30:02.5Z"
    }
  },
  "status": {
    "success": true,
    "exit_code": 0,
    "error_message": null
  },
  "metadata": {
    "burnit_version": "0.1.0",
    "timestamp": "2024-01-15T10:30:02.5Z",
    "repository_path": "/path/to/repo"
  }
}
```

### Result Storage

- Results are stored in `burnit-results.json` in the repository root
- Multiple command executions append to the same file
- The file contains an array of results for easy processing and analysis
- Only commands that output valid JSON matching the schema are captured
- Regular command output (non-JSON) is not affected

### Example Usage

#### Command that outputs JSON results
```bash
# Your benchmark script outputs JSON matching the schema
burnit path/to/repo --command "./benchmark.sh" --ref main

# Results are automatically captured in burnit-results.json
cat path/to/repo/burnit-results.json
```

#### Creating a compatible command
```bash
#!/bin/bash
# benchmark.sh - Example script that outputs compatible JSON

# Run your actual benchmark/test
start_time=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
# ... your benchmark logic here ...
end_time=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

# Output JSON results
cat << EOF
{
  "task": {
    "name": "performance-benchmark",
    "description": "Application performance benchmark",
    "command": "./benchmark.sh",
    "git_reference": "main"
  },
  "performance": {
    "memory_usage": {
      "peak_memory_mb": 128.5,
      "average_memory_mb": 95.2
    },
    "execution_time": {
      "elapsed_seconds": 15.3,
      "start_time": "$start_time",
      "end_time": "$end_time"
    }
  },
  "status": {
    "success": true,
    "exit_code": 0,
    "error_message": null
  },
  "metadata": {
    "burnit_version": "0.1.0",
    "timestamp": "$end_time",
    "repository_path": "$(pwd)"
  }
}
EOF
```

#### Analyzing results
```bash
# View all captured results
jq '.results' burnit-results.json

# Get performance metrics from latest result
jq '.results[-1].performance' burnit-results.json

# Compare execution times across runs
jq '.results[].performance.execution_time.elapsed_seconds' burnit-results.json
```

## Safety Guarantees

- **Always Restores State**: Uses RAII (Resource Acquisition Is Initialization) to ensure Git state is restored even if the process is interrupted
- **Uncommitted Changes Warning**: Warns when the original state had uncommitted changes
- **Safe Checkout**: Uses Git's safe checkout mode to prevent data loss
- **Error Recovery**: Handles various failure scenarios gracefully

## Error Handling

Burnit provides clear error messages for common issues:

- Repository not found or invalid
- Git reference doesn't exist
- Command execution failures
- Git operation errors
- Permission issues

## Logging

Set the `RUST_LOG` environment variable to control logging verbosity:

```bash
# Basic logging
RUST_LOG=info burnit path/to/repo --command "make test" --ref main

# Detailed logging
RUST_LOG=debug burnit path/to/repo --command "make test" --ref main

# All logging
RUST_LOG=trace burnit path/to/repo --command "make test" --ref main
```

## Development

### Prerequisites

- Rust 1.70 or later
- Git

### Building

```bash
cargo build
```

### Running Tests

```bash
# Run all tests
cargo test

# Run only unit tests
cargo test --lib

# Run only integration tests
cargo test --test integration_tests

# Run with logging
RUST_LOG=debug cargo test
```

### Test Repository

The project includes a test fixture repository at `test/fixtures/test_repo` that is automatically created by the setup script. This repository contains:

- Multiple branches (`main`, `feature/test-branch`)
- Tags (`v1.0.0`, `v1.1.0`)
- A test script for validation
- Various commits for testing different scenarios

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Built with [clap](https://github.com/clap-rs/clap) for CLI argument parsing
- Uses [git2](https://github.com/rust-lang/git2-rs) for Git operations
- JSON processing with [serde](https://github.com/serde-rs/serde) and [serde_json](https://github.com/serde-rs/json)
- System information via [sysinfo](https://github.com/GuillaumeGomez/sysinfo)
- Error handling powered by [thiserror](https://github.com/dtolnay/thiserror) and [anyhow](https://github.com/dtolnay/anyhow)
