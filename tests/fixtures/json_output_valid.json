{"task": {"name": "test-task", "description": "A test task for validation", "command": "echo test", "git_reference": "main"}, "performance": {"memory_usage": {"peak_memory_mb": 45.2, "average_memory_mb": 32.1}, "execution_time": {"elapsed_seconds": 2.5, "start_time": "2024-01-15T10:30:00Z", "end_time": "2024-01-15T10:30:02.5Z"}}, "status": {"success": true, "exit_code": 0, "error_message": null}, "metadata": {"burnit_version": "0.1.0", "timestamp": "2024-01-15T10:30:02.5Z", "repository_path": "/path/to/repo"}}