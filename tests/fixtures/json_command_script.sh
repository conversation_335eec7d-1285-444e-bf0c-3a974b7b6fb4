#!/bin/bash
# Script that outputs valid JSON matching our schema

cat << 'EOF'
{
  "task": {
    "name": "fixture-test",
    "description": "Test fixture command that outputs <PERSON><PERSON><PERSON>",
    "command": "./json_command_script.sh",
    "git_reference": "main"
  },
  "performance": {
    "memory_usage": {
      "peak_memory_mb": 25.5,
      "average_memory_mb": 20.1
    },
    "execution_time": {
      "elapsed_seconds": 1.2,
      "start_time": "2024-01-15T10:30:00Z",
      "end_time": "2024-01-15T10:30:01.2Z"
    }
  },
  "status": {
    "success": true,
    "exit_code": 0,
    "error_message": null
  },
  "metadata": {
    "burnit_version": "0.1.0",
    "timestamp": "2024-01-15T10:30:01.2Z",
    "repository_path": "/tmp/test"
  }
}
EOF
