use assert_cmd::Command;
use predicates::prelude::*;
use std::path::Path;
use std::process;
use tempfile::TempDir;

/// Copy fixture files to a directory and make scripts executable
fn copy_fixtures_to_dir(dest_dir: &Path) -> std::io::Result<()> {
    let fixtures_dir = Path::new("tests/fixtures");

    // Copy fixture files
    let files = ["README.md", "simple_test.sh", "test_script.sh", "feature.txt", "feature_test.sh"];

    for file in &files {
        let src = fixtures_dir.join(file);
        let dest = dest_dir.join(file);

        if src.exists() {
            std::fs::copy(&src, &dest)?;

            // Make shell scripts executable
            if file.ends_with(".sh") {
                #[cfg(unix)]
                {
                    use std::os::unix::fs::PermissionsExt;
                    let mut perms = std::fs::metadata(&dest)?.permissions();
                    perms.set_mode(0o755);
                    std::fs::set_permissions(&dest, perms)?;
                }
            }
        }
    }

    Ok(())
}

/// Create a fresh test repository for each test using fixture files
fn create_test_repo() -> TempDir {
    let temp_dir = TempDir::new().expect("Failed to create temp directory");
    let repo_path = temp_dir.path().join("test_repo");

    // Create the repository directory
    std::fs::create_dir_all(&repo_path).expect("Failed to create repo directory");

    // Initialize git repository
    let output = process::Command::new("git")
        .args(&["init"])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to initialize git repository");

    if !output.status.success() {
        panic!("Failed to initialize git repository: {}", String::from_utf8_lossy(&output.stderr));
    }

    // Configure git
    process::Command::new("git")
        .args(&["config", "user.name", "Test User"])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to configure git user name");

    process::Command::new("git")
        .args(&["config", "user.email", "<EMAIL>"])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to configure git user email");

    // Copy initial commit files
    copy_fixtures_to_dir(&repo_path).expect("Failed to copy fixture files");

    // Remove files that shouldn't be in initial commit
    std::fs::remove_file(repo_path.join("test_script.sh")).ok();
    std::fs::remove_file(repo_path.join("feature.txt")).ok();
    std::fs::remove_file(repo_path.join("feature_test.sh")).ok();

    process::Command::new("git")
        .args(&["add", "."])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to add files");

    process::Command::new("git")
        .args(&["commit", "-m", "Initial commit"])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to create initial commit");

    // Add test script in second commit
    copy_fixtures_to_dir(&repo_path).expect("Failed to copy fixture files again");
    std::fs::remove_file(repo_path.join("feature.txt")).ok();
    std::fs::remove_file(repo_path.join("feature_test.sh")).ok();

    process::Command::new("git")
        .args(&["add", "test_script.sh"])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to add test script");

    process::Command::new("git")
        .args(&["commit", "-m", "Add test script"])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to commit test script");

    // Create feature branch
    process::Command::new("git")
        .args(&["checkout", "-b", "feature/test-branch"])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to create feature branch");

    // Add feature files
    copy_fixtures_to_dir(&repo_path).expect("Failed to copy fixture files for feature");

    process::Command::new("git")
        .args(&["add", "feature.txt", "feature_test.sh"])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to add feature files");

    process::Command::new("git")
        .args(&["commit", "-m", "Add feature branch content"])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to commit feature content");

    // Create tag
    process::Command::new("git")
        .args(&["tag", "v1.0.0"])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to create tag");

    // Go back to main
    process::Command::new("git")
        .args(&["checkout", "main"])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to checkout main");

    // Create another commit on main
    std::fs::write(repo_path.join("README.md"), "# Test Repository\nThis is a test repository for burnit CLI testing.\nMain branch update\n")
        .expect("Failed to update README.md");

    process::Command::new("git")
        .args(&["add", "README.md"])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to add updated README");

    process::Command::new("git")
        .args(&["commit", "-m", "Update README on main"])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to commit README update");

    // Create another tag
    process::Command::new("git")
        .args(&["tag", "v1.1.0"])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to create second tag");

    temp_dir
}

#[test]
fn test_burnit_help() {
    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg("--help");

    cmd.assert()
        .success()
        .stdout(predicate::str::contains("Execute commands against different Git references"))
        .stdout(predicate::str::contains("--command"))
        .stdout(predicate::str::contains("--ref"));
}

#[test]
fn test_burnit_version() {
    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg("--version");

    cmd.assert()
        .success()
        .stdout(predicate::str::contains("burnit"));
}

#[test]
fn test_execute_command_on_main_branch() {
    let temp_dir = create_test_repo();
    let repo_path = temp_dir.path().join("test_repo");

    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg(&repo_path)
        .arg("--command")
        .arg("./test_script.sh")
        .arg("--ref")
        .arg("main");

    cmd.assert()
        .success()
        .stdout(predicate::str::contains("Test script executed successfully!"));
}

#[test]
fn test_execute_command_on_feature_branch() {
    let temp_dir = create_test_repo();
    let repo_path = temp_dir.path().join("test_repo");

    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg(&repo_path)
        .arg("--command")
        .arg("./test_script.sh")
        .arg("--ref")
        .arg("feature/test-branch");

    cmd.assert()
        .success()
        .stdout(predicate::str::contains("Test script executed successfully!"));
}

#[test]
fn test_execute_command_on_tag() {
    let temp_dir = create_test_repo();
    let repo_path = temp_dir.path().join("test_repo");

    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg(&repo_path)
        .arg("--command")
        .arg("./test_script.sh")
        .arg("--ref")
        .arg("v1.0.0");

    cmd.assert()
        .success()
        .stdout(predicate::str::contains("Test script executed successfully!"));
}

#[test]
fn test_execute_command_on_commit_sha() {
    let temp_dir = create_test_repo();
    let repo_path = temp_dir.path().join("test_repo");

    // Get the commit SHA of the initial commit
    let output = std::process::Command::new("git")
        .args(&["log", "--oneline", "--reverse"])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to get git log");

    let log_output = String::from_utf8(output.stdout).unwrap();
    let first_line = log_output.lines().next().unwrap();
    let commit_sha = first_line.split_whitespace().next().unwrap();

    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg(&repo_path)
        .arg("--command")
        .arg("./simple_test.sh") // Use the script that exists in the initial commit
        .arg("--ref")
        .arg(commit_sha);

    cmd.assert()
        .success()
        .stdout(predicate::str::contains("Initial commit test successful"));
}

#[test]
fn test_invalid_repository_path() {
    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg("/nonexistent/path")
        .arg("--command")
        .arg("echo test")
        .arg("--ref")
        .arg("main");

    cmd.assert()
        .failure()
        .stderr(predicate::str::contains("Repository not found"));
}

#[test]
fn test_invalid_git_reference() {
    let temp_dir = create_test_repo();
    let repo_path = temp_dir.path().join("test_repo");

    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg(&repo_path)
        .arg("--command")
        .arg("echo test")
        .arg("--ref")
        .arg("nonexistent-branch");

    cmd.assert()
        .failure()
        .stderr(predicate::str::contains("Git reference not found"));
}

#[test]
fn test_command_failure() {
    let temp_dir = create_test_repo();
    let repo_path = temp_dir.path().join("test_repo");

    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg(&repo_path)
        .arg("--command")
        .arg("false") // Command that always fails
        .arg("--ref")
        .arg("main");

    cmd.assert()
        .failure()
        .stderr(predicate::str::contains("Command execution failed"));
}

#[test]
fn test_git_state_restoration() {
    let temp_dir = create_test_repo();
    let repo_path = temp_dir.path().join("test_repo");

    // Get the current branch before running burnit
    let output = std::process::Command::new("git")
        .args(&["branch", "--show-current"])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to get current branch");
    let original_branch = String::from_utf8(output.stdout).unwrap().trim().to_string();

    // Run burnit on a different branch
    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg(&repo_path)
        .arg("--command")
        .arg("echo test")
        .arg("--ref")
        .arg("feature/test-branch");

    cmd.assert().success();

    // Check that we're back on the original branch
    let output = std::process::Command::new("git")
        .args(&["branch", "--show-current"])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to get current branch after burnit");
    let current_branch = String::from_utf8(output.stdout).unwrap().trim().to_string();

    assert_eq!(original_branch, current_branch, "Git state was not properly restored");
}

#[test]
fn test_missing_required_arguments() {
    // Test missing repository path
    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg("--command").arg("echo test").arg("--ref").arg("main");
    cmd.assert().failure();

    // Test missing command
    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg("/tmp").arg("--ref").arg("main");
    cmd.assert().failure();

    // Test missing ref
    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg("/tmp").arg("--command").arg("echo test");
    cmd.assert().failure();
}
