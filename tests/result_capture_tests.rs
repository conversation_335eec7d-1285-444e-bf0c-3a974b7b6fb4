use assert_cmd::Command;
use std::fs;
use std::path::Path;
use std::process;
use tempfile::TempDir;
use burnit::result::ResultsContainer;

/// Copy fixture files to a directory and make scripts executable
fn copy_fixtures_to_dir(dest_dir: &Path) -> std::io::Result<()> {
    let fixtures_dir = Path::new("tests/fixtures");

    // Copy fixture files including new JSON test scripts
    let files = [
        "README.md",
        "simple_test.sh",
        "test_script.sh",
        "feature.txt",
        "feature_test.sh",
        "json_command_script.sh",
        "non_json_command_script.sh"
    ];

    for file in &files {
        let src = fixtures_dir.join(file);
        let dest = dest_dir.join(file);

        if src.exists() {
            std::fs::copy(&src, &dest)?;

            // Make shell scripts executable
            if file.ends_with(".sh") {
                #[cfg(unix)]
                {
                    use std::os::unix::fs::PermissionsExt;
                    let mut perms = std::fs::metadata(&dest)?.permissions();
                    perms.set_mode(0o755);
                    std::fs::set_permissions(&dest, perms)?;
                }
            }
        }
    }

    Ok(())
}

/// Create a fresh test repository for each test using fixture files
fn create_test_repo_with_json_scripts() -> TempDir {
    let temp_dir = TempDir::new().expect("Failed to create temp directory");
    let repo_path = temp_dir.path().join("test_repo");

    // Create the repository directory
    std::fs::create_dir_all(&repo_path).expect("Failed to create repo directory");

    // Initialize git repository
    let output = process::Command::new("git")
        .args(&["init"])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to initialize git repository");

    if !output.status.success() {
        panic!("Failed to initialize git repository: {}", String::from_utf8_lossy(&output.stderr));
    }

    // Configure git
    process::Command::new("git")
        .args(&["config", "user.name", "Test User"])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to configure git user name");

    process::Command::new("git")
        .args(&["config", "user.email", "<EMAIL>"])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to configure git user email");

    // Copy all fixture files including JSON scripts
    copy_fixtures_to_dir(&repo_path).expect("Failed to copy fixture files");

    // Create initial commit with all files
    process::Command::new("git")
        .args(&["add", "."])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to add files");

    process::Command::new("git")
        .args(&["commit", "-m", "Initial commit with JSON test scripts"])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to create initial commit");

    temp_dir
}

#[test]
fn test_command_outputs_valid_json_result_is_captured() {
    // Given: A repository with a command that outputs valid JSON
    let temp_dir = create_test_repo_with_json_scripts();
    let repo_path = temp_dir.path().join("test_repo");
    let results_file = repo_path.join("burnit-results.json");

    // When: I execute the JSON-outputting command with burnit
    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg(&repo_path)
        .arg("--command")
        .arg("./json_command_script.sh")
        .arg("--ref")
        .arg("main");

    // Then: The command should succeed
    cmd.assert().success();

    // And: The results file should be created
    assert!(results_file.exists(), "burnit-results.json should be created");

    // And: The results file should contain the captured JSON result
    let content = fs::read_to_string(&results_file).unwrap();
    let container: ResultsContainer = serde_json::from_str(&content).unwrap();

    assert_eq!(container.results.len(), 1);
    let result = &container.results[0];
    assert_eq!(result.task.name, "fixture-test");
    assert_eq!(result.task.command, "./json_command_script.sh");
    assert_eq!(result.status.success, true);
}

#[test]
fn test_command_outputs_non_json_result_is_not_captured() {
    // Given: A repository with a command that outputs regular text
    let temp_dir = create_test_repo_with_json_scripts();
    let repo_path = temp_dir.path().join("test_repo");
    let results_file = repo_path.join("burnit-results.json");

    // When: I execute the non-JSON command with burnit
    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg(&repo_path)
        .arg("--command")
        .arg("./non_json_command_script.sh")
        .arg("--ref")
        .arg("main");

    // Then: The command should succeed
    cmd.assert().success();

    // And: No results file should be created
    assert!(!results_file.exists(), "burnit-results.json should not be created for non-JSON output");
}

#[test]
fn test_multiple_json_commands_append_results() {
    // Given: A repository with JSON-outputting commands
    let temp_dir = create_test_repo_with_json_scripts();
    let repo_path = temp_dir.path().join("test_repo");
    let results_file = repo_path.join("burnit-results.json");

    // When: I execute the first JSON command
    let mut cmd1 = Command::cargo_bin("burnit").unwrap();
    cmd1.arg(&repo_path)
        .arg("--command")
        .arg("./json_command_script.sh")
        .arg("--ref")
        .arg("main");

    cmd1.assert().success();

    // And: I execute the second JSON command
    let mut cmd2 = Command::cargo_bin("burnit").unwrap();
    cmd2.arg(&repo_path)
        .arg("--command")
        .arg("./json_command_script.sh")
        .arg("--ref")
        .arg("main");

    cmd2.assert().success();

    // Then: The results file should contain both results
    assert!(results_file.exists());

    let content = fs::read_to_string(&results_file).unwrap();
    let container: ResultsContainer = serde_json::from_str(&content).unwrap();

    assert_eq!(container.results.len(), 2, "Should have captured two JSON results");
}

#[test]
fn test_malformed_json_output_is_not_captured() {
    // Given: A repository with a command that outputs malformed JSON
    let temp_dir = create_test_repo_with_json_scripts();
    let repo_path = temp_dir.path().join("test_repo");
    let results_file = repo_path.join("burnit-results.json");

    // Create a script that outputs malformed JSON
    let malformed_script = repo_path.join("malformed_json_script.sh");
    fs::write(&malformed_script, r#"#!/bin/bash
echo '{"task": {"name": "incomplete"'
"#).unwrap();

    #[cfg(unix)]
    {
        use std::os::unix::fs::PermissionsExt;
        let mut perms = fs::metadata(&malformed_script).unwrap().permissions();
        perms.set_mode(0o755);
        fs::set_permissions(&malformed_script, perms).unwrap();
    }

    // When: I execute the malformed JSON command
    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg(&repo_path)
        .arg("--command")
        .arg("./malformed_json_script.sh")
        .arg("--ref")
        .arg("main");

    // Then: The command should succeed (the script itself runs fine)
    cmd.assert().success();

    // And: No results file should be created (malformed JSON is not captured)
    assert!(!results_file.exists(), "burnit-results.json should not be created for malformed JSON");
}

#[test]
fn test_results_file_location_is_in_repository_root() {
    // Given: A repository with a JSON-outputting command
    let temp_dir = create_test_repo_with_json_scripts();
    let repo_path = temp_dir.path().join("test_repo");
    let results_file = repo_path.join("burnit-results.json");

    // When: I execute the JSON command
    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg(&repo_path)
        .arg("--command")
        .arg("./json_command_script.sh")
        .arg("--ref")
        .arg("main");

    cmd.assert().success();

    // Then: The results file should be in the repository root
    assert!(results_file.exists());
    assert_eq!(results_file.parent().unwrap(), repo_path);
}
